import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/insurance/insurance_model.dart';

/// A model representing insurance coverage
class InsuranceCoverage {
  /// Unique identifier for the coverage
  final String id;

  /// Type of coverage
  final InsuranceCoverageType type;

  /// Coverage amount
  final double amount;

  /// Currency of the coverage amount
  final String currency;

  /// Deductible amount
  final double? deductible;

  /// Maximum benefit amount
  final double? maximumBenefit;

  /// Whether the coverage is included in the policy
  final bool isIncluded;

  /// Additional details about the coverage
  final Map<String, dynamic> details;

  /// Creates a new insurance coverage
  const InsuranceCoverage({
    required this.id,
    required this.type,
    required this.amount,
    required this.currency,
    this.deductible,
    this.maximumBenefit,
    required this.isIncluded,
    this.details = const {},
  });

  /// Get the formatted coverage amount
  String get formattedAmount {
    return '$currency${amount.toStringAsFixed(2)}';
  }

  /// Get the formatted deductible amount
  String? get formattedDeductible {
    if (deductible == null) return null;
    return '$currency${deductible!.toStringAsFixed(2)}';
  }

  /// Get the formatted maximum benefit amount
  String? get formattedMaximumBenefit {
    if (maximumBenefit == null) return null;
    return '$currency${maximumBenefit!.toStringAsFixed(2)}';
  }

  /// Creates a copy with some fields replaced
  InsuranceCoverage copyWith({
    String? id,
    InsuranceCoverageType? type,
    double? amount,
    String? currency,
    double? deductible,
    double? maximumBenefit,
    bool? isIncluded,
    Map<String, dynamic>? details,
  }) {
    return InsuranceCoverage(
      id: id ?? this.id,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      deductible: deductible ?? this.deductible,
      maximumBenefit: maximumBenefit ?? this.maximumBenefit,
      isIncluded: isIncluded ?? this.isIncluded,
      details: details ?? this.details,
    );
  }

  /// Creates from JSON
  factory InsuranceCoverage.fromJson(Map<String, dynamic> json) {
    return InsuranceCoverage(
      id: json['id'] as String,
      type: InsuranceCoverageType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
      ),
      amount: json['amount'] as double,
      currency: json['currency'] as String,
      deductible: json['deductible'] as double?,
      maximumBenefit: json['maximumBenefit'] as double?,
      isIncluded: json['isIncluded'] as bool,
      details: json['details'] as Map<String, dynamic>? ?? {},
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'amount': amount,
      'currency': currency,
      'deductible': deductible,
      'maximumBenefit': maximumBenefit,
      'isIncluded': isIncluded,
      'details': details,
    };
  }
}
