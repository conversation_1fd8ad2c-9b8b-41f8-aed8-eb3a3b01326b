import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

// Package imports
import 'package:timeline_tile/timeline_tile.dart';

// Project imports
import 'package:culture_connect/models/travel/insurance/insurance.dart';
import 'package:culture_connect/providers/travel/insurance/insurance_providers.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/error_view.dart';

/// A screen for displaying insurance claim details
class InsuranceClaimDetailsScreen extends ConsumerWidget {
  /// The ID of the claim to display
  final String claimId;

  /// Creates a new insurance claim details screen
  const InsuranceClaimDetailsScreen({
    super.key,
    required this.claimId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final claimAsync = ref.watch(claimProvider(claimId));

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Claim Details',
        showBackButton: true,
      ),
      body: claimAsync.when(
        data: (claim) {
          if (claim == null) {
            return Center(
              child: Text(
                'Claim not found',
                style: theme.textTheme.titleMedium,
              ),
            );
          }

          return SingleChildScrollView(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Claim header
                _buildClaimHeader(theme, claim),
                SizedBox(height: 24.h),

                // Claim status timeline
                Text(
                  'Claim Status',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16.h),
                _buildClaimStatusTimeline(theme, claim),
                SizedBox(height: 24.h),

                // Claim details
                Text(
                  'Claim Details',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16.h),
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.r),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildDetailItem(
                          theme,
                          'Reference Number',
                          claim.referenceNumber,
                        ),
                        SizedBox(height: 16.h),
                        _buildDetailItem(
                          theme,
                          'Incident Date',
                          DateFormat('MMM d, yyyy').format(claim.incidentDate),
                        ),
                        SizedBox(height: 16.h),
                        _buildDetailItem(
                          theme,
                          'Incident Location',
                          claim.incidentLocation,
                        ),
                        SizedBox(height: 16.h),
                        _buildDetailItem(
                          theme,
                          'Claim Amount',
                          claim.formattedClaimAmount,
                        ),
                        if (claim.approvedAmount != null) ...[
                          SizedBox(height: 16.h),
                          _buildDetailItem(
                            theme,
                            'Approved Amount',
                            claim.formattedApprovedAmount!,
                            valueColor: Colors.green,
                          ),
                        ],
                        SizedBox(height: 16.h),
                        _buildDetailItem(
                          theme,
                          'Submitted Date',
                          DateFormat('MMM d, yyyy').format(claim.submittedDate),
                        ),
                        if (claim.resolvedDate != null) ...[
                          SizedBox(height: 16.h),
                          _buildDetailItem(
                            theme,
                            'Resolved Date',
                            DateFormat('MMM d, yyyy')
                                .format(claim.resolvedDate!),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 24.h),

                // Incident description
                Text(
                  'Incident Description',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16.h),
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.r),
                    child: Text(
                      claim.incidentDescription,
                      style: theme.textTheme.bodyMedium,
                    ),
                  ),
                ),
                SizedBox(height: 24.h),

                // Supporting documents
                Text(
                  'Supporting Documents',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16.h),
                _buildDocumentsList(context, theme, claim),
                SizedBox(height: 24.h),

                // Additional information requested
                if (claim.additionalInfoRequested != null) ...[
                  Container(
                    padding: EdgeInsets.all(16.r),
                    decoration: BoxDecoration(
                      color: Colors.amber.withAlpha(25),
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: Colors.amber,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Colors.amber[800],
                              size: 24.r,
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              'Additional Information Requested',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.amber[800],
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          claim.additionalInfoRequested!,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.amber[800],
                          ),
                        ),
                        SizedBox(height: 16.h),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pushNamed(
                                context,
                                '/travel/insurance/claim/update',
                                arguments: claim.id,
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.amber[800],
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Provide Information'),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 24.h),
                ],

                // Denial reason
                if (claim.denialReason != null) ...[
                  Container(
                    padding: EdgeInsets.all(16.r),
                    decoration: BoxDecoration(
                      color: Colors.red.withAlpha(25),
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: Colors.red,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.red,
                              size: 24.r,
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              'Claim Denied',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          claim.denialReason!,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 24.h),
                ],

                // Policy information
                Text(
                  'Policy Information',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16.h),
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.r),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildDetailItem(
                          theme,
                          'Policy Name',
                          claim.policy.name,
                        ),
                        SizedBox(height: 16.h),
                        _buildDetailItem(
                          theme,
                          'Policy Number',
                          claim.policy.policyNumber ?? 'N/A',
                        ),
                        SizedBox(height: 16.h),
                        _buildDetailItem(
                          theme,
                          'Provider',
                          claim.policy.provider.name,
                        ),
                        SizedBox(height: 16.h),
                        _buildDetailItem(
                          theme,
                          'Coverage Type',
                          claim.coverageType.displayName,
                        ),
                        SizedBox(height: 16.h),
                        ElevatedButton.icon(
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              '/travel/insurance/policy',
                              arguments: claim.policy.id,
                            );
                          },
                          icon: const Icon(Icons.policy),
                          label: const Text('View Policy'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: theme.colorScheme.onPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 24.h),

                // Actions
                if (claim.status == InsuranceClaimStatus.infoRequested) ...[
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pushNamed(
                          context,
                          '/travel/insurance/claim/update',
                          arguments: claim.id,
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: theme.colorScheme.onPrimary,
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                      ),
                      child: const Text('Update Claim'),
                    ),
                  ),
                ],
              ],
            ),
          );
        },
        loading: () => const Center(
          child: LoadingIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: ErrorView(
            error: error.toString(),
            onRetry: () => ref.refresh(claimProvider(claimId)),
          ),
        ),
      ),
    );
  }

  Widget _buildClaimHeader(ThemeData theme, InsuranceClaim claim) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.r),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            colors: [
              claim.status.color.withAlpha(204), // 0.8 * 255 = 204
              claim.status.color.withAlpha(153), // 0.6 * 255 = 153
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        claim.coverageType.displayName,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'Ref: ${claim.referenceNumber}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.white.withAlpha(230), // 0.9 * 255 = 230
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.all(12.r),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Icon(
                    claim.coverageType.icon,
                    color: Colors.white,
                    size: 32.r,
                  ),
                ),
              ],
            ),
            SizedBox(height: 24.h),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Status',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white.withAlpha(179), // 0.7 * 255 = 179
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          claim.status.displayName,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Claim Amount',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white.withAlpha(179), // 0.7 * 255 = 179
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        claim.formattedClaimAmount,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (claim.approvedAmount != null) ...[
              SizedBox(height: 16.h),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Approved Amount',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color:
                                Colors.white.withAlpha(179), // 0.7 * 255 = 179
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          claim.formattedApprovedAmount!,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildClaimStatusTimeline(ThemeData theme, InsuranceClaim claim) {
    final statuses = [
      InsuranceClaimStatus.submitted,
      InsuranceClaimStatus.underReview,
      if (claim.additionalInfoRequested != null)
        InsuranceClaimStatus.infoRequested,
      if (claim.status == InsuranceClaimStatus.approved ||
          claim.status == InsuranceClaimStatus.partiallyApproved ||
          claim.status == InsuranceClaimStatus.paid)
        claim.status == InsuranceClaimStatus.partiallyApproved
            ? InsuranceClaimStatus.partiallyApproved
            : InsuranceClaimStatus.approved,
      if (claim.status == InsuranceClaimStatus.denied)
        InsuranceClaimStatus.denied,
      if (claim.status == InsuranceClaimStatus.paid) InsuranceClaimStatus.paid,
    ];

    final currentStatusIndex = statuses.indexOf(claim.status);

    return SizedBox(
      height: 100.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: statuses.length,
        itemBuilder: (context, index) {
          final status = statuses[index];
          final isActive = index <= currentStatusIndex;
          final isLast = index == statuses.length - 1;

          return SizedBox(
            width: 120.w,
            child: TimelineTile(
              axis: TimelineAxis.horizontal,
              alignment: TimelineAlign.center,
              isFirst: index == 0,
              isLast: isLast,
              indicatorStyle: IndicatorStyle(
                width: 24.r,
                height: 24.r,
                indicator: Container(
                  decoration: BoxDecoration(
                    color: isActive ? status.color : Colors.grey[300],
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    status.icon,
                    color: isActive ? Colors.white : Colors.grey[500],
                    size: 16.r,
                  ),
                ),
              ),
              beforeLineStyle: LineStyle(
                color: isActive ? status.color : Colors.grey.shade300,
                thickness: 2.r,
              ),
              afterLineStyle: LineStyle(
                color: index < currentStatusIndex
                    ? status.color
                    : Colors.grey.shade300,
                thickness: 2.r,
              ),
              endChild: Padding(
                padding: EdgeInsets.only(top: 8.h),
                child: Text(
                  status.displayName,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isActive ? status.color : Colors.grey[500],
                    fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              startChild: Padding(
                padding: EdgeInsets.only(bottom: 8.h),
                child: Text(
                  _getStatusDate(claim, status),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isActive ? Colors.black87 : Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  String _getStatusDate(InsuranceClaim claim, InsuranceClaimStatus status) {
    switch (status) {
      case InsuranceClaimStatus.submitted:
        return DateFormat('MM/dd/yy').format(claim.submittedDate);
      case InsuranceClaimStatus.underReview:
        if (claim.status == InsuranceClaimStatus.submitted) return '';
        // In a real app, this would come from the claim history
        return DateFormat('MM/dd/yy')
            .format(claim.submittedDate.add(const Duration(days: 1)));
      case InsuranceClaimStatus.infoRequested:
        if (claim.status == InsuranceClaimStatus.submitted ||
            claim.status == InsuranceClaimStatus.underReview) {
          return '';
        }
        // In a real app, this would come from the claim history
        return DateFormat('MM/dd/yy')
            .format(claim.submittedDate.add(const Duration(days: 2)));
      case InsuranceClaimStatus.approved:
      case InsuranceClaimStatus.partiallyApproved:
      case InsuranceClaimStatus.denied:
        if (claim.resolvedDate == null) return '';
        return DateFormat('MM/dd/yy').format(claim.resolvedDate!);
      case InsuranceClaimStatus.paid:
        if (claim.resolvedDate == null) return '';
        // In a real app, this would come from the claim history
        return DateFormat('MM/dd/yy')
            .format(claim.resolvedDate!.add(const Duration(days: 2)));
    }
  }

  Widget _buildDetailItem(
    ThemeData theme,
    String label,
    String value, {
    Color? valueColor,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: valueColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentsList(
      BuildContext context, ThemeData theme, InsuranceClaim claim) {
    if (claim.documentUrls.isEmpty) {
      return Card(
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Center(
            child: Text(
              'No documents uploaded',
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          children: claim.documentUrls.asMap().entries.map((entry) {
            final index = entry.key;
            final url = entry.value;

            return ListTile(
              leading: Container(
                padding: EdgeInsets.all(8.r),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.description,
                  color: theme.colorScheme.primary,
                  size: 24.r,
                ),
              ),
              title: Text(
                'Document ${index + 1}',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              subtitle: Text(
                url.split('/').last,
                style: theme.textTheme.bodySmall,
              ),
              trailing: IconButton(
                icon: const Icon(Icons.visibility),
                onPressed: () {
                  // In a real app, this would open the document
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Viewing document: ${url.split('/').last}'),
                      duration: const Duration(seconds: 2),
                    ),
                  );
                },
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
