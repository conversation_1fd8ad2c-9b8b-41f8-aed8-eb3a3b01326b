import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/models/payment_method.dart';
import 'package:culture_connect/screens/payment/payment_success_screen.dart';
import 'package:culture_connect/services/payment_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/payment/payment_method_selection.dart';

/// Screen for confirming a payment
class PaymentConfirmationScreen extends ConsumerStatefulWidget {
  /// The experience being booked
  final Experience experience;

  /// The booking details
  final Booking booking;

  /// Creates a new payment confirmation screen
  const PaymentConfirmationScreen({
    super.key,
    required this.experience,
    required this.booking,
  });

  @override
  ConsumerState<PaymentConfirmationScreen> createState() =>
      _PaymentConfirmationScreenState();
}

class _PaymentConfirmationScreenState
    extends ConsumerState<PaymentConfirmationScreen> {
  PaymentMethod? _selectedPaymentMethod;
  bool _isProcessing = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Payment Confirmation',
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Experience details
              _buildExperienceDetails(),
              const SizedBox(height: 24.0),

              // Booking summary
              _buildBookingSummary(),
              const SizedBox(height: 24.0),

              // Payment method selection
              PaymentMethodSelection(
                onPaymentMethodSelected: (method) {
                  setState(() {
                    _selectedPaymentMethod = method;
                    _errorMessage = null;
                  });
                },
              ),
              const SizedBox(height: 24.0),

              // Error message
              if (_errorMessage != null) ...[
                Container(
                  padding: const EdgeInsets.all(16.0),
                  decoration: BoxDecoration(
                    color: Colors.red.withAlpha(13), // shade50 equivalent
                    borderRadius: BorderRadius.circular(8.0),
                    border: Border.all(
                        color: Colors.red.withAlpha(51)), // shade200 equivalent
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 24.0,
                      ),
                      const SizedBox(width: 12.0),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 14.0,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24.0),
              ],

              // Payment button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isProcessing || _selectedPaymentMethod == null
                      ? null
                      : _processPayment,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  child: _isProcessing
                      ? const SizedBox(
                          width: 24.0,
                          height: 24.0,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2.0,
                          ),
                        )
                      : Text(
                          'Pay ${_formatAmount(widget.booking.totalAmount)}',
                          style: const TextStyle(
                            fontSize: 16.0,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
              const SizedBox(height: 16.0),

              // Security note
              const Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.lock,
                      size: 16.0,
                      color: Colors.grey,
                    ),
                    SizedBox(width: 8.0),
                    Text(
                      'Secure payment processed by CultureConnect',
                      style: TextStyle(
                        fontSize: 12.0,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExperienceDetails() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Experience image
        ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: Image.network(
            widget.experience.imageUrl,
            width: 80.0,
            height: 80.0,
            fit: BoxFit.cover,
          ),
        ),
        const SizedBox(width: 16.0),

        // Experience details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.experience.title,
                style: const TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4.0),
              Text(
                widget.experience.location,
                style: const TextStyle(
                  fontSize: 14.0,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 4.0),
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16.0,
                    color: AppTheme.primaryColor,
                  ),
                  const SizedBox(width: 4.0),
                  Text(
                    _formatDate(widget.booking.date),
                    style: const TextStyle(
                      fontSize: 14.0,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBookingSummary() {
    // Calculate service fee (10% of subtotal)
    final subtotal = widget.experience.price * widget.booking.participantCount;
    final serviceFee = subtotal * 0.10; // 10% service fee

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(13), // shade50 equivalent
        borderRadius: BorderRadius.circular(8.0),
        border:
            Border.all(color: Colors.grey.withAlpha(51)), // shade200 equivalent
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Booking Summary',
            style: TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16.0),

          // Participants
          _buildSummaryRow(
            'Participants',
            '${widget.booking.participantCount} ${widget.booking.participantCount == 1 ? 'person' : 'people'}',
          ),
          const SizedBox(height: 8.0),

          // Base price
          _buildSummaryRow(
            'Base Price',
            _formatAmount(widget.experience.price),
          ),
          const SizedBox(height: 8.0),

          // Subtotal
          _buildSummaryRow(
            'Subtotal',
            _formatAmount(subtotal),
          ),
          const SizedBox(height: 8.0),

          // Service fee
          _buildSummaryRow(
            'Service Fee',
            _formatAmount(serviceFee),
          ),

          // Divider
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Divider(
                height: 1,
                color: Colors.grey.withAlpha(77)), // shade300 equivalent
          ),

          // Total
          _buildSummaryRow(
            'Total',
            _formatAmount(widget.booking.totalAmount),
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16.0 : 14.0,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 16.0 : 14.0,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  String _formatAmount(double amount) {
    return '\$${amount.toStringAsFixed(2)}';
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];

    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  Future<void> _processPayment() async {
    if (_selectedPaymentMethod == null) {
      setState(() {
        _errorMessage = 'Please select a payment method';
      });
      return;
    }

    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    try {
      final paymentService = PaymentService();

      // Process the payment
      final result = await paymentService.processPayment(
        booking: widget.booking,
        provider: PaymentProvider.stripe, // Default to Stripe
        userEmail: '<EMAIL>', // In a real app, get from user profile
        userName: 'John Doe', // In a real app, get from user profile
        paymentMethodId: _selectedPaymentMethod?.id,
      );

      if (result.success) {
        // Get the receipt
        final receipt = await paymentService
            .getReceipt(result.additionalData!['receiptId']);

        if (mounted) {
          // Navigate to success screen
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => PaymentSuccessScreen(
                experience: widget.experience,
                booking: widget.booking,
                receipt: receipt!,
              ),
            ),
          );
        }
      } else {
        setState(() {
          _errorMessage = result.errorMessage ?? 'Payment failed';
          _isProcessing = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Payment failed: $e';
        _isProcessing = false;
      });
    }
  }
}
