import 'dart:async';
import 'package:culture_connect/models/currency/currency_model.dart';
import 'package:culture_connect/services/api/api_service.dart';
import 'package:culture_connect/services/storage/local_storage_service.dart';
import 'package:culture_connect/utils/connectivity_service.dart';
import 'package:culture_connect/utils/logger.dart';

/// Service for handling currency conversion operations
class CurrencyService {
  // Singleton instance
  static final CurrencyService _instance = CurrencyService._internal();
  factory CurrencyService() => _instance;
  CurrencyService._internal();

  // Logger
  final _logger = LoggingService();

  // Services
  late final ApiService _apiService;
  late final CacheService _cacheService;
  late final ConnectivityService _connectivityService;

  // State
  bool _isInitialized = false;
  bool _isOfflineMode = false;

  // Currency data
  final Map<String, CurrencyModel> _currencies = {};
  final Map<String, Map<String, double>> _exchangeRates = {};
  DateTime? _lastUpdated;

  // Default currency
  String _baseCurrency = 'USD';

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize services
      _cacheService = CacheService(_logger);
      _apiService = ApiService(_logger, _cacheService);
      _connectivityService = ConnectivityService();

      // Initialize connectivity service
      await _connectivityService.initialize();

      // Check connectivity
      _isOfflineMode = !_connectivityService.isConnected;

      // Load cached data
      _loadCachedData();

      // If online, fetch latest data
      if (!_isOfflineMode) {
        await _fetchCurrencies();
        await _fetchExchangeRates();
      }

      // Listen for connectivity changes
      _connectivityService.connectivityStream.listen(_handleConnectivityChange);

      _isInitialized = true;
      _logger.info('CurrencyService',
          'Service initialized, isOfflineMode: $_isOfflineMode');
    } catch (e) {
      _logger.error('CurrencyService', 'Error initializing service: $e');

      // Ensure we have at least cached data
      _loadCachedData();
      _isInitialized = true;
    }
  }

  /// Load cached currency data from storage
  void _loadCachedData() {
    try {
      final currencyData = _storageService.getJson('currency_data');
      if (currencyData != null) {
        // Load currencies
        final currenciesJson =
            currencyData['currencies'] as Map<String, dynamic>?;
        if (currenciesJson != null) {
          currenciesJson.forEach((code, data) {
            _currencies[code] =
                CurrencyModel.fromJson(data as Map<String, dynamic>);
          });
        }

        // Load exchange rates
        final ratesJson =
            currencyData['exchangeRates'] as Map<String, dynamic>?;
        if (ratesJson != null) {
          ratesJson.forEach((base, rates) {
            _exchangeRates[base] = (rates as Map<String, dynamic>).map(
              (currency, rate) => MapEntry(currency, (rate as num).toDouble()),
            );
          });
        }

        // Load last updated timestamp
        final lastUpdatedStr = currencyData['lastUpdated'] as String?;
        if (lastUpdatedStr != null) {
          _lastUpdated = DateTime.parse(lastUpdatedStr);
        }

        // Load base currency
        _baseCurrency = currencyData['baseCurrency'] as String? ?? 'USD';
      }

      _logger.info(
          'Loaded cached currency data: ${_currencies.length} currencies, ${_exchangeRates.length} exchange rate sets');
    } catch (e) {
      _logger.error('Error loading cached currency data: $e');
    }
  }

  /// Save currency data to cache
  Future<void> _saveCacheData() async {
    try {
      final currenciesJson = <String, dynamic>{};
      _currencies.forEach((code, currency) {
        currenciesJson[code] = currency.toJson();
      });

      final currencyData = {
        'currencies': currenciesJson,
        'exchangeRates': _exchangeRates,
        'lastUpdated': _lastUpdated?.toIso8601String(),
        'baseCurrency': _baseCurrency,
      };

      await _storageService.setJson('currency_data', currencyData);
      _logger.info('Saved currency data to cache');
    } catch (e) {
      _logger.error('Error saving currency data to cache: $e');
    }
  }

  /// Handle connectivity changes
  void _handleConnectivityChange(bool isConnected) {
    _isOfflineMode = !isConnected;
    _logger.info('Connectivity changed: ${isConnected ? 'Online' : 'Offline'}');

    if (isConnected) {
      // When coming back online, fetch latest data
      _fetchCurrencies();
      _fetchExchangeRates();
    }
  }

  /// Fetch currencies from API
  Future<void> _fetchCurrencies() async {
    try {
      final response = await _apiService.get('/api/v1/currencies');

      if (response.isSuccess) {
        final currenciesJson = response.data['currencies'] as List<dynamic>;

        // Clear existing currencies
        _currencies.clear();

        // Add new currencies
        for (final currencyJson in currenciesJson) {
          final currency =
              CurrencyModel.fromJson(currencyJson as Map<String, dynamic>);
          _currencies[currency.code] = currency;
        }

        // Save to cache
        await _saveCacheData();

        _logger.info('Fetched ${_currencies.length} currencies from API');
      } else {
        throw Exception(response.message ?? 'Failed to fetch currencies');
      }
    } catch (e) {
      _logger.error('Error fetching currencies: $e');
      // Fall back to cached data
    }
  }

  /// Fetch exchange rates from API
  Future<void> _fetchExchangeRates() async {
    try {
      final response = await _apiService.get('/api/v1/exchange-rates');

      if (response.isSuccess) {
        final ratesJson = response.data['rates'] as Map<String, dynamic>;

        // Clear existing rates
        _exchangeRates.clear();

        // Add new rates
        ratesJson.forEach((base, rates) {
          _exchangeRates[base] = (rates as Map<String, dynamic>).map(
            (currency, rate) => MapEntry(currency, (rate as num).toDouble()),
          );
        });

        // Update last updated timestamp
        _lastUpdated = DateTime.now();

        // Save to cache
        await _saveCacheData();

        _logger.info(
            'Fetched exchange rates for ${_exchangeRates.length} base currencies from API');
      } else {
        throw Exception(response.message ?? 'Failed to fetch exchange rates');
      }
    } catch (e) {
      _logger.error('Error fetching exchange rates: $e');
      // Fall back to cached data
    }
  }

  /// Get all available currencies
  List<CurrencyModel> getAllCurrencies() {
    return _currencies.values.toList();
  }

  /// Get a currency by code
  CurrencyModel? getCurrency(String code) {
    return _currencies[code.toUpperCase()];
  }

  /// Get the exchange rate between two currencies
  double? getExchangeRate(String fromCurrency, String toCurrency) {
    final from = fromCurrency.toUpperCase();
    final to = toCurrency.toUpperCase();

    // If same currency, rate is 1
    if (from == to) return 1.0;

    // Direct lookup
    if (_exchangeRates.containsKey(from) &&
        _exchangeRates[from]!.containsKey(to)) {
      return _exchangeRates[from]![to];
    }

    // Try reverse lookup
    if (_exchangeRates.containsKey(to) &&
        _exchangeRates[to]!.containsKey(from)) {
      return 1.0 / _exchangeRates[to]![from]!;
    }

    // Try via base currency
    if (_exchangeRates.containsKey(_baseCurrency)) {
      if (_exchangeRates[_baseCurrency]!.containsKey(from) &&
          _exchangeRates[_baseCurrency]!.containsKey(to)) {
        final fromRate = _exchangeRates[_baseCurrency]![from]!;
        final toRate = _exchangeRates[_baseCurrency]![to]!;
        return toRate / fromRate;
      }
    }

    // No rate found
    return null;
  }

  /// Convert an amount from one currency to another
  double? convert(double amount, String fromCurrency, String toCurrency) {
    final rate = getExchangeRate(fromCurrency, toCurrency);
    if (rate == null) return null;
    return amount * rate;
  }

  /// Get the last updated timestamp
  DateTime? getLastUpdated() {
    return _lastUpdated;
  }

  /// Set the base currency
  void setBaseCurrency(String currencyCode) {
    _baseCurrency = currencyCode.toUpperCase();
    _saveCacheData();
  }

  /// Get the base currency
  String getBaseCurrency() {
    return _baseCurrency;
  }

  /// Force refresh of currency data
  Future<void> refreshCurrencyData() async {
    if (_isOfflineMode) {
      _logger.warning('Cannot refresh currency data in offline mode');
      return;
    }

    try {
      await _fetchCurrencies();
      await _fetchExchangeRates();
      _logger.info('Currency data refreshed successfully');
    } catch (e) {
      _logger.error('Error refreshing currency data: $e');
      rethrow;
    }
  }

  /// Check if the service is in offline mode
  bool isOfflineMode() {
    return _isOfflineMode;
  }

  /// Toggle offline mode manually
  Future<void> setOfflineMode(bool enabled) async {
    if (_isOfflineMode == enabled) return;

    _isOfflineMode = enabled;
    _logger.info('Offline mode ${enabled ? 'enabled' : 'disabled'} manually');

    if (!enabled) {
      // When coming back online, fetch latest data
      await refreshCurrencyData();
    }
  }

  /// Format currency amount according to the currency's format
  String formatCurrency(double amount, String currencyCode) {
    final currency = getCurrency(currencyCode);
    if (currency == null) {
      return amount.toStringAsFixed(2);
    }

    // Simple formatting - in a real app, you would use intl package
    return '${currency.symbol}${amount.toStringAsFixed(2)}';
  }
}
