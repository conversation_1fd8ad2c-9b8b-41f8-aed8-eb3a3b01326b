import 'dart:io';
import 'package:uuid/uuid.dart';

import 'package:culture_connect/models/travel/insurance/insurance.dart';
import 'package:culture_connect/models/payment/payment_method_model.dart';
import 'package:culture_connect/services/travel/insurance/insurance_api_service.dart';
import 'package:culture_connect/utils/connectivity_service.dart';
import 'package:culture_connect/utils/logger.dart';

/// Service for managing insurance policies and claims
class InsuranceService {
  // Singleton instance
  static final InsuranceService _instance = InsuranceService._internal();
  factory InsuranceService() => _instance;
  InsuranceService._internal();

  final _uuid = const Uuid();
  final _logger = Logger('InsuranceService');
  final _apiService = InsuranceApiService();
  final _connectivityService = ConnectivityService();

  // In-memory storage (for offline mode and caching)
  final List<InsurancePolicy> _policies = [];
  final List<InsuranceClaim> _claims = [];
  final List<InsuranceProvider> _providers = [];

  bool _isInitialized = false;
  bool _isOfflineMode = false;

  /// Initialize the service with data from API or mock data if offline
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize connectivity service
      await _connectivityService.initialize();

      // Check connectivity
      _isOfflineMode = !(await _connectivityService.checkConnectivity());

      if (_isOfflineMode) {
        _logger.info('Initializing insurance service in offline mode');
        // Generate mock data for offline mode
        await _generateMockData();
      } else {
        _logger.info('Initializing insurance service with API data');
        // Fetch data from API
        await _fetchDataFromApi();
      }

      // Listen for connectivity changes
      _connectivityService.connectivityStream.listen(_handleConnectivityChange);

      _isInitialized = true;
    } catch (e) {
      _logger.error('Error initializing insurance service: $e');
      // Fall back to mock data if initialization fails
      await _generateMockData();
      _isInitialized = true;
    }
  }

  /// Handle connectivity changes
  void _handleConnectivityChange(bool isConnected) {
    _isOfflineMode = !isConnected;
    _logger.info('Connectivity changed: ${isConnected ? 'Online' : 'Offline'}');

    if (isConnected) {
      // Sync data with API when coming back online
      _syncDataWithApi();
    }
  }

  /// Fetch data from API
  Future<void> _fetchDataFromApi() async {
    try {
      // Fetch providers
      final providers = await _apiService.getInsuranceProviders();
      _providers.clear();
      _providers.addAll(providers);

      // Fetch policies
      final policies = await _apiService.getAvailablePolicies();
      _policies.clear();
      _policies.addAll(policies);

      // Fetch claims
      final claims = await _apiService.getClaims();
      _claims.clear();
      _claims.addAll(claims);
    } catch (e) {
      _logger.error('Error fetching data from API: $e');
      // Fall back to mock data if API calls fail
      await _generateMockData();
    }
  }

  /// Sync data with API when coming back online
  Future<void> _syncDataWithApi() async {
    _logger.info('Syncing data with API');
    // In a real app, this would sync any changes made while offline
    // For now, we'll just refresh the data
    await _fetchDataFromApi();
  }

  /// Get all available insurance policies
  Future<List<InsurancePolicy>> getAvailablePolicies() async {
    await initialize();

    if (_isOfflineMode) {
      _logger.info('Getting available policies from local storage');
      return _policies
          .where((policy) =>
              policy.status == InsurancePolicyStatus.active ||
              policy.status == InsurancePolicyStatus.pending)
          .toList();
    } else {
      try {
        _logger.info('Getting available policies from API');
        final policies = await _apiService.getAvailablePolicies();

        // Update local cache
        _policies.clear();
        _policies.addAll(policies);

        return policies;
      } catch (e) {
        _logger.error('Error getting available policies from API: $e');
        // Fall back to local cache
        return _policies
            .where((policy) =>
                policy.status == InsurancePolicyStatus.active ||
                policy.status == InsurancePolicyStatus.pending)
            .toList();
      }
    }
  }

  /// Get all purchased insurance policies for the current user
  Future<List<InsurancePolicy>> getPurchasedPolicies() async {
    await initialize();
    // In a real app, this would filter by the current user's ID
    return _policies
        .where((policy) =>
            policy.policyNumber != null &&
            policy.status != InsurancePolicyStatus.pending)
        .toList();
  }

  /// Get active insurance policies for the current user
  Future<List<InsurancePolicy>> getActivePolicies() async {
    await initialize();
    return _policies
        .where((policy) =>
            policy.status == InsurancePolicyStatus.active &&
            policy.policyNumber != null)
        .toList();
  }

  /// Get an insurance policy by ID
  Future<InsurancePolicy?> getPolicyById(String policyId) async {
    await initialize();
    try {
      return _policies.firstWhere((policy) => policy.id == policyId);
    } catch (e) {
      return null;
    }
  }

  /// Get all insurance providers
  Future<List<InsuranceProvider>> getInsuranceProviders() async {
    await initialize();
    return _providers;
  }

  /// Get an insurance provider by ID
  Future<InsuranceProvider?> getProviderById(String providerId) async {
    await initialize();
    try {
      return _providers.firstWhere((provider) => provider.id == providerId);
    } catch (e) {
      return null;
    }
  }

  /// Get featured insurance providers
  Future<List<InsuranceProvider>> getFeaturedProviders() async {
    await initialize();
    return _providers.where((provider) => provider.isFeatured).toList();
  }

  /// Get partner insurance providers
  Future<List<InsuranceProvider>> getPartnerProviders() async {
    await initialize();
    return _providers.where((provider) => provider.isPartner).toList();
  }

  /// Get all insurance claims for the current user
  Future<List<InsuranceClaim>> getClaims() async {
    await initialize();
    // In a real app, this would filter by the current user's ID
    return _claims;
  }

  /// Get an insurance claim by ID
  Future<InsuranceClaim?> getClaimById(String claimId) async {
    await initialize();
    try {
      return _claims.firstWhere((claim) => claim.id == claimId);
    } catch (e) {
      return null;
    }
  }

  /// Get insurance claims by policy ID
  Future<List<InsuranceClaim>> getClaimsByPolicyId(String policyId) async {
    await initialize();
    return _claims.where((claim) => claim.policy.id == policyId).toList();
  }

  /// Get active insurance claims
  Future<List<InsuranceClaim>> getActiveClaims() async {
    await initialize();
    return _claims
        .where((claim) =>
            claim.status != InsuranceClaimStatus.paid &&
            claim.status != InsuranceClaimStatus.denied)
        .toList();
  }

  /// Purchase an insurance policy
  Future<InsurancePolicy> purchasePolicy({
    required InsurancePolicy policy,
    required DateTime startDate,
    required DateTime endDate,
    required List<String> destinationCountries,
    required int travelerCount,
    required PaymentMethodModel paymentMethod,
  }) async {
    await initialize();

    if (_isOfflineMode) {
      _logger.info('Purchasing policy in offline mode');

      // Generate a policy number
      final policyNumber = 'POL-${_uuid.v4().substring(0, 8).toUpperCase()}';

      // Create a purchased policy
      final purchasedPolicy = policy.copyWith(
        id: _uuid.v4(),
        startDate: startDate,
        endDate: endDate,
        status: InsurancePolicyStatus.active,
        policyNumber: policyNumber,
        destinationCountries: destinationCountries,
        travelerCount: travelerCount,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Add to in-memory storage
      _policies.add(purchasedPolicy);

      return purchasedPolicy;
    } else {
      try {
        _logger.info('Purchasing policy via API');

        // Create traveler information
        final travelers = List.generate(
            travelerCount,
            (index) => {
                  'firstName': 'Traveler',
                  'lastName': '${index + 1}',
                  'email': 'traveler${index + 1}@example.com',
                  'phone': '+1234567890',
                  'dateOfBirth': DateTime.now()
                      .subtract(const Duration(days: 365 * 30))
                      .toIso8601String(),
                  'passportNumber': '',
                });

        // Purchase policy via API
        final purchasedPolicy = await _apiService.purchasePolicy(
          policyId: policy.id,
          startDate: startDate,
          endDate: endDate,
          destinationCountries: destinationCountries,
          travelerCount: travelerCount,
          paymentMethod: paymentMethod,
          travelers: travelers,
        );

        // Update local cache
        _policies.add(purchasedPolicy);

        return purchasedPolicy;
      } catch (e) {
        _logger.error('Error purchasing policy via API: $e');

        // Fall back to offline mode if API call fails
        return purchasePolicy(
          policy: policy,
          startDate: startDate,
          endDate: endDate,
          destinationCountries: destinationCountries,
          travelerCount: travelerCount,
          paymentMethod: paymentMethod,
        );
      }
    }
  }

  /// Cancel an insurance policy
  Future<InsurancePolicy> cancelPolicy(String policyId) async {
    await initialize();

    if (_isOfflineMode) {
      _logger.info('Cancelling policy in offline mode');

      // Find the policy
      final index = _policies.indexWhere((policy) => policy.id == policyId);
      if (index == -1) {
        throw Exception('Policy not found');
      }

      // Update the policy status
      final policy = _policies[index];
      final updatedPolicy = policy.copyWith(
        status: InsurancePolicyStatus.cancelled,
        updatedAt: DateTime.now(),
      );

      // Update in-memory storage
      _policies[index] = updatedPolicy;

      return updatedPolicy;
    } else {
      try {
        _logger.info('Cancelling policy via API');

        // Cancel policy via API
        final updatedPolicy = await _apiService.cancelPolicy(policyId);

        // Update local cache
        final index = _policies.indexWhere((policy) => policy.id == policyId);
        if (index >= 0) {
          _policies[index] = updatedPolicy;
        } else {
          _policies.add(updatedPolicy);
        }

        return updatedPolicy;
      } catch (e) {
        _logger.error('Error cancelling policy via API: $e');

        // Fall back to offline mode
        _isOfflineMode = true;
        return cancelPolicy(policyId);
      }
    }
  }

  /// File an insurance claim
  Future<InsuranceClaim> fileClaim({
    required String policyId,
    required InsuranceCoverageType coverageType,
    required DateTime incidentDate,
    required String incidentDescription,
    required String incidentLocation,
    required double claimAmount,
    required String currency,
    required List<File> documents,
  }) async {
    await initialize();

    // Find the policy
    final policy = await getPolicyById(policyId);
    if (policy == null) {
      throw Exception('Policy not found');
    }

    if (_isOfflineMode) {
      _logger.info('Filing claim in offline mode');

      // In a real app, this would upload documents to a server and get URLs
      final documentUrls = documents
          .map((doc) => 'https://example.com/documents/${_uuid.v4()}')
          .toList();

      // Generate a reference number
      final referenceNumber = 'CLM-${_uuid.v4().substring(0, 8).toUpperCase()}';

      // Create a new claim
      final claim = InsuranceClaim(
        id: _uuid.v4(),
        policy: policy,
        coverageType: coverageType,
        status: InsuranceClaimStatus.submitted,
        incidentDate: incidentDate,
        incidentDescription: incidentDescription,
        incidentLocation: incidentLocation,
        claimAmount: claimAmount,
        currency: currency,
        submittedDate: DateTime.now(),
        lastUpdatedDate: DateTime.now(),
        documentUrls: documentUrls,
        referenceNumber: referenceNumber,
      );

      // Add to in-memory storage
      _claims.add(claim);

      return claim;
    } else {
      try {
        _logger.info('Filing claim via API');

        // File claim via API
        final claim = await _apiService.fileClaim(
          policyId: policyId,
          coverageType: coverageType,
          incidentDate: incidentDate,
          incidentDescription: incidentDescription,
          incidentLocation: incidentLocation,
          claimAmount: claimAmount,
          currency: currency,
          documents: documents,
        );

        // Update local cache
        _claims.add(claim);

        return claim;
      } catch (e) {
        _logger.error('Error filing claim via API: $e');

        // Fall back to offline mode if API call fails
        _isOfflineMode = true;
        return fileClaim(
          policyId: policyId,
          coverageType: coverageType,
          incidentDate: incidentDate,
          incidentDescription: incidentDescription,
          incidentLocation: incidentLocation,
          claimAmount: claimAmount,
          currency: currency,
          documents: documents,
        );
      }
    }
  }

  /// Update an insurance claim
  Future<InsuranceClaim> updateClaim({
    required String claimId,
    InsuranceClaimStatus? status,
    String? incidentDescription,
    double? claimAmount,
    List<String>? additionalDocumentUrls,
    List<File>? additionalDocuments,
    String? additionalInfoRequested,
  }) async {
    await initialize();

    // Find the claim
    final index = _claims.indexWhere((claim) => claim.id == claimId);
    if (index == -1) {
      throw Exception('Claim not found');
    }

    if (_isOfflineMode) {
      _logger.info('Updating claim in offline mode');

      // Update the claim
      final claim = _claims[index];
      final updatedClaim = claim.copyWith(
        status: status ?? claim.status,
        incidentDescription: incidentDescription ?? claim.incidentDescription,
        claimAmount: claimAmount ?? claim.claimAmount,
        documentUrls: additionalDocumentUrls != null
            ? [...claim.documentUrls, ...additionalDocumentUrls]
            : claim.documentUrls,
        additionalInfoRequested:
            additionalInfoRequested ?? claim.additionalInfoRequested,
        lastUpdatedDate: DateTime.now(),
        resolvedDate: status == InsuranceClaimStatus.approved ||
                status == InsuranceClaimStatus.partiallyApproved ||
                status == InsuranceClaimStatus.denied ||
                status == InsuranceClaimStatus.paid
            ? DateTime.now()
            : claim.resolvedDate,
      );

      // Update in-memory storage
      _claims[index] = updatedClaim;

      return updatedClaim;
    } else {
      try {
        _logger.info('Updating claim via API');

        // Update claim via API
        final updatedClaim = await _apiService.updateClaim(
          claimId: claimId,
          status: status,
          incidentDescription: incidentDescription,
          claimAmount: claimAmount,
          additionalDocuments: additionalDocuments,
          additionalInfoRequested: additionalInfoRequested,
        );

        // Update local cache
        final index = _claims.indexWhere((claim) => claim.id == claimId);
        if (index >= 0) {
          _claims[index] = updatedClaim;
        } else {
          _claims.add(updatedClaim);
        }

        return updatedClaim;
      } catch (e) {
        _logger.error('Error updating claim via API: $e');

        // Fall back to offline mode if API call fails
        _isOfflineMode = true;
        return updateClaim(
          claimId: claimId,
          status: status,
          incidentDescription: incidentDescription,
          claimAmount: claimAmount,
          additionalDocumentUrls: additionalDocumentUrls,
          additionalInfoRequested: additionalInfoRequested,
        );
      }
    }
  }

  /// Get recommended policies based on destination and trip details
  Future<List<InsurancePolicy>> getRecommendedPolicies({
    required List<String> destinationCountries,
    required DateTime startDate,
    required DateTime endDate,
    required int travelerCount,
    List<InsuranceCoverageType>? desiredCoverageTypes,
  }) async {
    await initialize();

    if (_isOfflineMode) {
      _logger.info('Getting recommended policies from local storage');

      // In a real app, this would use an algorithm to recommend policies
      // For now, we'll just return all active policies sorted by relevance
      final policies = await getAvailablePolicies();

      // Filter by coverage types if specified
      final filteredPolicies = desiredCoverageTypes != null
          ? policies.where((policy) {
              final coverageTypes = policy.coverages
                  .where((coverage) => coverage.isIncluded)
                  .map((coverage) => coverage.type)
                  .toSet();
              return desiredCoverageTypes
                  .every((type) => coverageTypes.contains(type));
            }).toList()
          : policies;

      // Sort by relevance (in a real app, this would be more sophisticated)
      filteredPolicies.sort((a, b) {
        // Prioritize policies that cover all destination countries
        final aCoversAllCountries = a.destinationCountries.isEmpty ||
            destinationCountries
                .every((country) => a.destinationCountries.contains(country));
        final bCoversAllCountries = b.destinationCountries.isEmpty ||
            destinationCountries
                .every((country) => b.destinationCountries.contains(country));

        if (aCoversAllCountries && !bCoversAllCountries) return -1;
        if (!aCoversAllCountries && bCoversAllCountries) return 1;

        // Then sort by price
        return a.price.compareTo(b.price);
      });

      return filteredPolicies;
    } else {
      try {
        _logger.info('Getting recommended policies from API');

        // Get recommended policies from API
        final policies = await _apiService.getRecommendedPolicies(
          destinationCountries: destinationCountries,
          startDate: startDate,
          endDate: endDate,
          travelerCount: travelerCount,
          desiredCoverageTypes: desiredCoverageTypes,
        );

        // Update local cache
        for (final policy in policies) {
          final index = _policies.indexWhere((p) => p.id == policy.id);
          if (index >= 0) {
            _policies[index] = policy;
          } else {
            _policies.add(policy);
          }
        }

        return policies;
      } catch (e) {
        _logger.error('Error getting recommended policies from API: $e');

        // Fall back to offline mode
        _isOfflineMode = true;
        return getRecommendedPolicies(
          destinationCountries: destinationCountries,
          startDate: startDate,
          endDate: endDate,
          travelerCount: travelerCount,
          desiredCoverageTypes: desiredCoverageTypes,
        );
      }
    }
  }

  /// Compare insurance policies
  Future<Map<String, List<dynamic>>> comparePolicies(
      List<String> policyIds) async {
    await initialize();

    if (_isOfflineMode) {
      _logger.info('Comparing policies from local storage');

      // Get the policies
      final policies = <InsurancePolicy>[];
      for (final id in policyIds) {
        final policy = await getPolicyById(id);
        if (policy != null) {
          policies.add(policy);
        }
      }

      // Create a comparison table
      final comparison = <String, List<dynamic>>{};

      // Basic information
      comparison['name'] = policies.map((p) => p.name).toList();
      comparison['provider'] = policies.map((p) => p.provider.name).toList();
      comparison['price'] = policies.map((p) => p.formattedPrice).toList();
      comparison['type'] = policies.map((p) => p.type.displayName).toList();

      // Coverage information
      for (final type in InsuranceCoverageType.values) {
        comparison[type.displayName] = policies.map((p) {
          final coverage = p.coverages.firstWhere(
            (c) => c.type == type && c.isIncluded,
            orElse: () => InsuranceCoverage(
              id: '',
              type: type,
              amount: 0,
              currency: p.currency,
              isIncluded: false,
            ),
          );
          return coverage.isIncluded ? coverage.formattedAmount : 'Not covered';
        }).toList();
      }

      return comparison;
    } else {
      try {
        _logger.info('Comparing policies via API');

        // Compare policies via API
        final comparison = await _apiService.comparePolicies(policyIds);

        return comparison;
      } catch (e) {
        _logger.error('Error comparing policies via API: $e');

        // Fall back to offline mode
        _isOfflineMode = true;
        return comparePolicies(policyIds);
      }
    }
  }

  /// Generate mock data for testing
  Future<void> _generateMockData() async {
    // Generate providers
    _generateMockProviders();

    // Generate policies
    _generateMockPolicies();

    // Generate claims
    _generateMockClaims();
  }

  /// Generate mock providers
  void _generateMockProviders() {
    _providers.addAll([
      InsuranceProvider(
        id: '1',
        name: 'Global Protect Insurance',
        description: 'Comprehensive travel insurance for global travelers.',
        logoUrl: 'https://via.placeholder.com/150?text=Global+Protect',
        websiteUrl: 'https://example.com/global-protect',
        phoneNumber: '******-123-4567',
        email: '<EMAIL>',
        rating: 4.7,
        reviewCount: 1245,
        isFeatured: true,
        isPartner: true,
        countries: [
          'United States',
          'Canada',
          'United Kingdom',
          'Australia',
          'Japan'
        ],
      ),
      InsuranceProvider(
        id: '2',
        name: 'SafeJourney Insurance',
        description:
            'Affordable travel insurance with excellent customer service.',
        logoUrl: 'https://via.placeholder.com/150?text=SafeJourney',
        websiteUrl: 'https://example.com/safejourney',
        phoneNumber: '******-987-6543',
        email: '<EMAIL>',
        rating: 4.5,
        reviewCount: 987,
        isFeatured: true,
        isPartner: true,
        countries: ['United States', 'Canada', 'Mexico', 'Brazil', 'Argentina'],
      ),
      InsuranceProvider(
        id: '3',
        name: 'TravelGuard',
        description: 'Specialized insurance for adventure travelers.',
        logoUrl: 'https://via.placeholder.com/150?text=TravelGuard',
        websiteUrl: 'https://example.com/travelguard',
        phoneNumber: '******-456-7890',
        email: '<EMAIL>',
        rating: 4.3,
        reviewCount: 756,
        isFeatured: false,
        isPartner: true,
        countries: [
          'United States',
          'Canada',
          'New Zealand',
          'Australia',
          'South Africa'
        ],
      ),
    ]);
  }

  /// Generate mock policies
  void _generateMockPolicies() {
    // Create coverage options
    final medicalCoverage = InsuranceCoverage(
      id: '1',
      type: InsuranceCoverageType.medical,
      amount: 100000,
      currency: 'USD',
      deductible: 100,
      isIncluded: true,
    );

    final tripCancellationCoverage = InsuranceCoverage(
      id: '2',
      type: InsuranceCoverageType.tripCancellation,
      amount: 5000,
      currency: 'USD',
      isIncluded: true,
    );

    final baggageLossCoverage = InsuranceCoverage(
      id: '3',
      type: InsuranceCoverageType.baggageLoss,
      amount: 2000,
      currency: 'USD',
      deductible: 50,
      isIncluded: true,
    );

    final flightDelayCoverage = InsuranceCoverage(
      id: '4',
      type: InsuranceCoverageType.flightDelay,
      amount: 500,
      currency: 'USD',
      isIncluded: true,
    );

    final emergencyEvacuationCoverage = InsuranceCoverage(
      id: '5',
      type: InsuranceCoverageType.emergencyEvacuation,
      amount: 250000,
      currency: 'USD',
      isIncluded: true,
    );

    final accidentalDeathCoverage = InsuranceCoverage(
      id: '6',
      type: InsuranceCoverageType.accidentalDeath,
      amount: 50000,
      currency: 'USD',
      isIncluded: true,
    );

    final rentalCarDamageCoverage = InsuranceCoverage(
      id: '7',
      type: InsuranceCoverageType.rentalCarDamage,
      amount: 35000,
      currency: 'USD',
      deductible: 250,
      isIncluded: false,
    );

    final adventureActivitiesCoverage = InsuranceCoverage(
      id: '8',
      type: InsuranceCoverageType.adventureActivities,
      amount: 10000,
      currency: 'USD',
      isIncluded: false,
    );

    // Create policies
    _policies.addAll([
      // Global Protect policies
      InsurancePolicy(
        id: '1',
        name: 'Essential Coverage',
        description: 'Basic travel insurance for budget-conscious travelers.',
        type: InsurancePolicyType.singleTrip,
        provider: _providers[0], // Global Protect
        price: 45.99,
        currency: 'USD',
        coverages: [
          medicalCoverage.copyWith(amount: 50000),
          tripCancellationCoverage.copyWith(amount: 2500),
          baggageLossCoverage.copyWith(amount: 1000),
          flightDelayCoverage,
          emergencyEvacuationCoverage.copyWith(amount: 100000),
          accidentalDeathCoverage.copyWith(isIncluded: false),
          rentalCarDamageCoverage.copyWith(isIncluded: false),
          adventureActivitiesCoverage.copyWith(isIncluded: false),
        ],
        status: InsurancePolicyStatus.active,
        destinationCountries: [],
        travelerCount: 1,
        isRefundable: true,
        refundPolicy: 'Full refund if cancelled 14 days before departure.',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      InsurancePolicy(
        id: '2',
        name: 'Premium Coverage',
        description: 'Comprehensive travel insurance with extensive benefits.',
        type: InsurancePolicyType.singleTrip,
        provider: _providers[0], // Global Protect
        price: 89.99,
        currency: 'USD',
        coverages: [
          medicalCoverage.copyWith(amount: 150000),
          tripCancellationCoverage.copyWith(amount: 7500),
          baggageLossCoverage.copyWith(amount: 3000),
          flightDelayCoverage.copyWith(amount: 750),
          emergencyEvacuationCoverage.copyWith(amount: 300000),
          accidentalDeathCoverage,
          rentalCarDamageCoverage.copyWith(isIncluded: true),
          adventureActivitiesCoverage.copyWith(isIncluded: false),
        ],
        status: InsurancePolicyStatus.active,
        destinationCountries: [],
        travelerCount: 1,
        isRefundable: true,
        refundPolicy: 'Full refund if cancelled 7 days before departure.',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      InsurancePolicy(
        id: '3',
        name: 'Annual Multi-Trip',
        description:
            'Comprehensive coverage for multiple trips throughout the year.',
        type: InsurancePolicyType.annualMultiTrip,
        provider: _providers[0], // Global Protect
        price: 249.99,
        currency: 'USD',
        coverages: [
          medicalCoverage.copyWith(amount: 200000),
          tripCancellationCoverage.copyWith(amount: 10000),
          baggageLossCoverage.copyWith(amount: 5000),
          flightDelayCoverage.copyWith(amount: 1000),
          emergencyEvacuationCoverage.copyWith(amount: 500000),
          accidentalDeathCoverage.copyWith(amount: 100000),
          rentalCarDamageCoverage.copyWith(isIncluded: true),
          adventureActivitiesCoverage.copyWith(isIncluded: true),
        ],
        status: InsurancePolicyStatus.active,
        destinationCountries: [],
        travelerCount: 1,
        isRefundable: false,
        refundPolicy: 'Non-refundable after 14 days from purchase.',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
      ),

      // SafeJourney policies
      InsurancePolicy(
        id: '4',
        name: 'Basic Protection',
        description: 'Affordable travel insurance for essential coverage.',
        type: InsurancePolicyType.singleTrip,
        provider: _providers[1], // SafeJourney
        price: 39.99,
        currency: 'USD',
        coverages: [
          medicalCoverage.copyWith(amount: 40000),
          tripCancellationCoverage.copyWith(amount: 2000),
          baggageLossCoverage.copyWith(amount: 800),
          flightDelayCoverage.copyWith(amount: 300),
          emergencyEvacuationCoverage.copyWith(amount: 75000),
          accidentalDeathCoverage.copyWith(isIncluded: false),
          rentalCarDamageCoverage.copyWith(isIncluded: false),
          adventureActivitiesCoverage.copyWith(isIncluded: false),
        ],
        status: InsurancePolicyStatus.active,
        destinationCountries: [],
        travelerCount: 1,
        isRefundable: true,
        refundPolicy: 'Full refund if cancelled 14 days before departure.',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      InsurancePolicy(
        id: '5',
        name: 'Family Plan',
        description: 'Comprehensive coverage for the whole family.',
        type: InsurancePolicyType.family,
        provider: _providers[1], // SafeJourney
        price: 129.99,
        currency: 'USD',
        coverages: [
          medicalCoverage.copyWith(amount: 100000),
          tripCancellationCoverage.copyWith(amount: 5000),
          baggageLossCoverage.copyWith(amount: 2500),
          flightDelayCoverage.copyWith(amount: 600),
          emergencyEvacuationCoverage.copyWith(amount: 200000),
          accidentalDeathCoverage.copyWith(amount: 75000),
          rentalCarDamageCoverage.copyWith(isIncluded: true),
          adventureActivitiesCoverage.copyWith(isIncluded: false),
        ],
        status: InsurancePolicyStatus.active,
        destinationCountries: [],
        travelerCount: 4,
        isRefundable: true,
        refundPolicy: 'Full refund if cancelled 10 days before departure.',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
      ),

      // TravelGuard policies
      InsurancePolicy(
        id: '6',
        name: 'Adventure Seeker',
        description:
            'Specialized coverage for adventure activities and extreme sports.',
        type: InsurancePolicyType.adventure,
        provider: _providers[2], // TravelGuard
        price: 99.99,
        currency: 'USD',
        coverages: [
          medicalCoverage.copyWith(amount: 150000),
          tripCancellationCoverage.copyWith(amount: 4000),
          baggageLossCoverage.copyWith(amount: 2000),
          flightDelayCoverage.copyWith(amount: 500),
          emergencyEvacuationCoverage.copyWith(amount: 350000),
          accidentalDeathCoverage.copyWith(amount: 75000),
          rentalCarDamageCoverage.copyWith(isIncluded: true),
          adventureActivitiesCoverage.copyWith(isIncluded: true, amount: 25000),
        ],
        status: InsurancePolicyStatus.active,
        destinationCountries: [],
        travelerCount: 1,
        isRefundable: true,
        refundPolicy: 'Full refund if cancelled 14 days before departure.',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      InsurancePolicy(
        id: '7',
        name: 'Business Traveler',
        description:
            'Specialized coverage for business travelers with equipment protection.',
        type: InsurancePolicyType.business,
        provider: _providers[2], // TravelGuard
        price: 79.99,
        currency: 'USD',
        coverages: [
          medicalCoverage.copyWith(amount: 100000),
          tripCancellationCoverage.copyWith(amount: 5000),
          baggageLossCoverage.copyWith(amount: 3500),
          flightDelayCoverage.copyWith(amount: 750),
          emergencyEvacuationCoverage.copyWith(amount: 200000),
          accidentalDeathCoverage.copyWith(amount: 100000),
          rentalCarDamageCoverage.copyWith(isIncluded: true),
          adventureActivitiesCoverage.copyWith(isIncluded: false),
        ],
        status: InsurancePolicyStatus.active,
        destinationCountries: [],
        travelerCount: 1,
        isRefundable: true,
        refundPolicy: 'Full refund if cancelled 7 days before departure.',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 30)),
      ),

      // Example of a purchased policy
      InsurancePolicy(
        id: '8',
        name: 'Premium Coverage',
        description: 'Comprehensive travel insurance with extensive benefits.',
        type: InsurancePolicyType.singleTrip,
        provider: _providers[0], // Global Protect
        price: 89.99,
        currency: 'USD',
        coverages: [
          medicalCoverage.copyWith(amount: 150000),
          tripCancellationCoverage.copyWith(amount: 7500),
          baggageLossCoverage.copyWith(amount: 3000),
          flightDelayCoverage.copyWith(amount: 750),
          emergencyEvacuationCoverage.copyWith(amount: 300000),
          accidentalDeathCoverage,
          rentalCarDamageCoverage.copyWith(isIncluded: true),
          adventureActivitiesCoverage.copyWith(isIncluded: false),
        ],
        startDate: DateTime.now().subtract(const Duration(days: 5)),
        endDate: DateTime.now().add(const Duration(days: 10)),
        status: InsurancePolicyStatus.active,
        policyNumber: 'POL-12345678',
        destinationCountries: ['France', 'Italy', 'Spain'],
        travelerCount: 2,
        isRefundable: true,
        refundPolicy: 'Full refund if cancelled 7 days before departure.',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now().subtract(const Duration(days: 15)),
      ),
    ]);
  }

  /// Generate mock claims
  void _generateMockClaims() {
    // Find a purchased policy
    final policy = _policies.firstWhere(
      (p) => p.policyNumber != null,
      orElse: () => _policies.first,
    );

    _claims.addAll([
      InsuranceClaim(
        id: '1',
        policy: policy,
        coverageType: InsuranceCoverageType.baggageLoss,
        status: InsuranceClaimStatus.approved,
        incidentDate: DateTime.now().subtract(const Duration(days: 3)),
        incidentDescription:
            'My luggage was lost during a flight transfer in Paris. The airline has confirmed the loss.',
        incidentLocation: 'Charles de Gaulle Airport, Paris, France',
        claimAmount: 750.00,
        currency: 'USD',
        approvedAmount: 750.00,
        submittedDate: DateTime.now().subtract(const Duration(days: 2)),
        lastUpdatedDate: DateTime.now().subtract(const Duration(days: 1)),
        resolvedDate: DateTime.now().subtract(const Duration(hours: 12)),
        documentUrls: [
          'https://example.com/documents/airline-confirmation.pdf',
          'https://example.com/documents/luggage-receipt.pdf',
        ],
        referenceNumber: 'CLM-12345678',
      ),
      InsuranceClaim(
        id: '2',
        policy: policy,
        coverageType: InsuranceCoverageType.medical,
        status: InsuranceClaimStatus.underReview,
        incidentDate: DateTime.now().subtract(const Duration(days: 2)),
        incidentDescription:
            'I developed a severe allergic reaction and had to visit a hospital in Rome.',
        incidentLocation: 'Rome, Italy',
        claimAmount: 1200.00,
        currency: 'USD',
        submittedDate: DateTime.now().subtract(const Duration(days: 1)),
        lastUpdatedDate: DateTime.now().subtract(const Duration(hours: 6)),
        documentUrls: [
          'https://example.com/documents/medical-report.pdf',
          'https://example.com/documents/hospital-bill.pdf',
          'https://example.com/documents/prescription.pdf',
        ],
        referenceNumber: 'CLM-87654321',
      ),
    ]);
  }
}
