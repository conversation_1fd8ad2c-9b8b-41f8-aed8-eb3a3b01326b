import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/insurance/insurance.dart';
import 'package:culture_connect/services/travel/insurance/insurance_service.dart';

/// Provider for the insurance service
final insuranceServiceProvider = Provider<InsuranceService>((ref) {
  return InsuranceService();
});

/// Provider for available insurance policies
final availablePoliciesProvider = FutureProvider<List<InsurancePolicy>>((ref) async {
  final insuranceService = ref.watch(insuranceServiceProvider);
  return insuranceService.getAvailablePolicies();
});

/// Provider for purchased insurance policies
final purchasedPoliciesProvider = FutureProvider<List<InsurancePolicy>>((ref) async {
  final insuranceService = ref.watch(insuranceServiceProvider);
  return insuranceService.getPurchasedPolicies();
});

/// Provider for active insurance policies
final activePoliciesProvider = FutureProvider<List<InsurancePolicy>>((ref) async {
  final insuranceService = ref.watch(insuranceServiceProvider);
  return insuranceService.getActivePolicies();
});

/// Provider for a specific insurance policy
final policyProvider = FutureProvider.family<InsurancePolicy?, String>((ref, policyId) async {
  final insuranceService = ref.watch(insuranceServiceProvider);
  return insuranceService.getPolicyById(policyId);
});

/// Provider for insurance providers
final insuranceProvidersProvider = FutureProvider<List<InsuranceProvider>>((ref) async {
  final insuranceService = ref.watch(insuranceServiceProvider);
  return insuranceService.getInsuranceProviders();
});

/// Provider for featured insurance providers
final featuredProvidersProvider = FutureProvider<List<InsuranceProvider>>((ref) async {
  final insuranceService = ref.watch(insuranceServiceProvider);
  return insuranceService.getFeaturedProviders();
});

/// Provider for partner insurance providers
final partnerProvidersProvider = FutureProvider<List<InsuranceProvider>>((ref) async {
  final insuranceService = ref.watch(insuranceServiceProvider);
  return insuranceService.getPartnerProviders();
});

/// Provider for a specific insurance provider
final providerProvider = FutureProvider.family<InsuranceProvider?, String>((ref, providerId) async {
  final insuranceService = ref.watch(insuranceServiceProvider);
  return insuranceService.getProviderById(providerId);
});

/// Provider for insurance claims
final claimsProvider = FutureProvider<List<InsuranceClaim>>((ref) async {
  final insuranceService = ref.watch(insuranceServiceProvider);
  return insuranceService.getClaims();
});

/// Provider for active insurance claims
final activeClaimsProvider = FutureProvider<List<InsuranceClaim>>((ref) async {
  final insuranceService = ref.watch(insuranceServiceProvider);
  return insuranceService.getActiveClaims();
});

/// Provider for a specific insurance claim
final claimProvider = FutureProvider.family<InsuranceClaim?, String>((ref, claimId) async {
  final insuranceService = ref.watch(insuranceServiceProvider);
  return insuranceService.getClaimById(claimId);
});

/// Provider for claims by policy ID
final claimsByPolicyProvider = FutureProvider.family<List<InsuranceClaim>, String>((ref, policyId) async {
  final insuranceService = ref.watch(insuranceServiceProvider);
  return insuranceService.getClaimsByPolicyId(policyId);
});

/// Provider for recommended policies
final recommendedPoliciesProvider = FutureProvider.family<List<InsurancePolicy>, Map<String, dynamic>>((ref, params) async {
  final insuranceService = ref.watch(insuranceServiceProvider);
  return insuranceService.getRecommendedPolicies(
    destinationCountries: (params['destinationCountries'] as List<dynamic>).cast<String>(),
    startDate: params['startDate'] as DateTime,
    endDate: params['endDate'] as DateTime,
    travelerCount: params['travelerCount'] as int,
    desiredCoverageTypes: params['desiredCoverageTypes'] != null
        ? (params['desiredCoverageTypes'] as List<dynamic>).cast<InsuranceCoverageType>()
        : null,
  );
});

/// Provider for policy comparison
final policyComparisonProvider = FutureProvider.family<Map<String, List<dynamic>>, List<String>>((ref, policyIds) async {
  final insuranceService = ref.watch(insuranceServiceProvider);
  return insuranceService.comparePolicies(policyIds);
});
